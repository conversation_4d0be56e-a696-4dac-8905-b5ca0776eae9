import numpy as np
import torch
from typing import Dict, List, Tuple, Optional
from collections import defaultdict
import logging
from dataclasses import dataclass


@dataclass
class TripletInfo:
    """Struktura pro metadata o tripletu"""
    anchor: str
    positive: str
    negative: str
    anchor_class: str
    positive_score: float
    negative_score: float
    difficulty: float


@dataclass
class IterationStats:
    """Statistiky iterace"""
    total_triplets: int
    avg_difficulty: float
    class_distribution: Dict[str, int]
    difficulty_range: Tuple[float, float]
    threshold_stats: Dict[str, int]


class TripletCreator:
    """
    Třída pro vytváření tripletů podle strategie hard mining
    s adaptivními prahy a curriculum learning
    """

    def __init__(self, model, positive_threshold: float = 0.8, negative_threshold: float = 0.65):
        """
        Args:
            model: SBertClassifier nebo SentenceTransformer model
            positive_threshold: Práh pro špatné pozitivní příklady (nižš<PERSON> skóre = horší)
            negative_threshold: Pr<PERSON>h pro nebezpečné negativní příklady (v<PERSON><PERSON><PERSON><PERSON> skóre = hor<PERSON><PERSON>)
        """
        self.model = model
        self.positive_threshold = positive_threshold
        self.negative_threshold = negative_threshold
        self.logger = logging.getLogger(__name__)

    def calculate_scores(self, class_data: Dict[str, List[str]]) -> Dict[str, List[Tuple[str, float, float]]]:
        """
        Spočítá skóre pro každý příklad vůči všem třídám

        Args:
            class_data: {třída: [seznam_textů]}

        Returns:
            Slovník {třída: [(text, score_vůči_vlastní_třídě, score_vůči_nejbližší_cizí_třídě)]}
        """
        if not class_data:
            return {}

        self.logger.info(f"Počítám skóre pro {len(class_data)} tříd...")

        all_texts = []
        text_to_class = {}

        # Připravíme všechny texty a mapování
        for class_name, texts in class_data.items():
            for text in texts:
                all_texts.append(text)
                text_to_class[text] = class_name

        if not all_texts:
            return {}

        # Vypočítáme embeddingy pro všechny texty
        self.logger.info(f"Počítám embeddingy pro {len(all_texts)} textů...")

        # Použití method z SBertClassifier nebo přímé volání
        if hasattr(self.model, 'encode'):
            embeddings = self.model.encode(all_texts)
            embeddings = torch.tensor(embeddings)
        else:
            # Fallback pro SentenceTransformer
            embeddings = self.model.encode(all_texts, convert_to_tensor=True, normalize_embeddings=True)

        # Normalizace embeddings (pokud už nejsou)
        if embeddings.norm(dim=1).mean() > 1.1:  # Pokud nejsou normalizované
            embeddings = embeddings / embeddings.norm(dim=1, keepdim=True)

        # Vypočítáme prototypy pro každou třídu
        class_prototypes = {}
        for class_name, texts in class_data.items():
            # Najdeme indexy textů této třídy
            class_indices = [i for i, text in enumerate(all_texts) if text_to_class[text] == class_name]
            if class_indices:
                class_embeddings = embeddings[class_indices]
                prototype = class_embeddings.mean(dim=0)
                class_prototypes[class_name] = prototype / prototype.norm()

        # Spočítáme skóre pro každý text
        results = defaultdict(list)

        for i, text in enumerate(all_texts):
            text_embedding = embeddings[i]
            true_class = text_to_class[text]

            # Skóre vůči vlastní třídě
            own_score = torch.nn.functional.cosine_similarity(
                text_embedding, class_prototypes[true_class], dim=0
            ).item()

            # Skóre vůči nejbližší cizí třídě
            other_scores = []
            for class_name, prototype in class_prototypes.items():
                if class_name != true_class:
                    score = torch.nn.functional.cosine_similarity(
                        text_embedding, prototype, dim=0
                    ).item()
                    other_scores.append(score)

            closest_other = max(other_scores) if other_scores else 0.0

            results[true_class].append((text, own_score, closest_other))

        self.logger.info(f"Skóre vypočítána pro {len(results)} tříd")
        return results

    def create_triplets_advanced(self, scored_data: Dict[str, List[Tuple[str, float, float]]],
                                 class_data: Dict[str, List[str]], top_n: int = 5) -> List[TripletInfo]:
        """
        Vytvoří triplety s detailními metadata podle vylepšené strategie

        Args:
            scored_data: Výstup z calculate_scores
            class_data: Původní data tříd pro anchor selection
            top_n: Počet nejhorších příkladů na třídu

        Returns:
            Seznam TripletInfo objektů
        """
        triplets = []

        for class_name, examples in scored_data.items():
            # Seřadíme příklady podle skóre vůči vlastní třídě (nejhorší první)
            examples.sort(key=lambda x: x[1])

            # Vybereme anchor z původních textů třídy (nejlepší reprezentant)
            class_texts = class_data.get(class_name, [])
            if not class_texts:
                continue

            # Anchor = nejlepší příklad třídy (nebo první text - kotva)
            anchor_text = class_texts[0]  # Kotva z Excelu

            # Vybereme TOP N nejhorších pozitivních příkladů
            worst_positives = [ex for ex in examples if ex[1] < self.positive_threshold][:top_n]

            for positive_text, positive_score, negative_score in worst_positives:
                # Najdeme vhodný negativní příklad
                best_negative = None
                best_negative_score = -1

                for other_class, other_examples in scored_data.items():
                    if other_class != class_name:
                        # Hledáme negativní příklady s vysokým skóre vůči aktuální třídě
                        for other_text, other_own_score, other_neg_score in other_examples:
                            # Skóre tohoto textu vůči naší třídě
                            if other_neg_score > self.negative_threshold and other_neg_score > best_negative_score:
                                best_negative = other_text
                                best_negative_score = other_neg_score

                if best_negative:
                    # Vypočítáme obtížnost tripletu
                    difficulty = self.calculate_triplet_difficulty(anchor_text, positive_text, best_negative)

                    triplet_info = TripletInfo(
                        anchor=anchor_text,
                        positive=positive_text,
                        negative=best_negative,
                        anchor_class=class_name,
                        positive_score=positive_score,
                        negative_score=best_negative_score,
                        difficulty=difficulty
                    )
                    triplets.append(triplet_info)

        self.logger.info(f"Vytvořeno {len(triplets)} tripletů")
        return triplets

    def create_triplets(self, scored_data: Dict[str, List[Tuple[str, float, float]]],
                        class_data: Dict[str, List[str]], top_n: int = 5) -> List[Tuple[str, str, str]]:
        """
        Vytvoří triplety v původním formátu pro zpětnou kompatibilitu
        """
        advanced_triplets = self.create_triplets_advanced(scored_data, class_data, top_n)
        return [(t.anchor, t.positive, t.negative) for t in advanced_triplets]

    def calculate_triplet_difficulty(self, anchor: str, positive: str, negative: str) -> float:
        """
        Spočítá obtížnost konkrétního tripletu

        Args:
            anchor, positive, negative: Texty tripletu

        Returns:
            Obtížnost v rozsahu 0-1 (vyšší = těžší)
        """
        try:
            # Vypočítáme embeddingy
            embeddings = self.model.encode([anchor, positive, negative])
            if not isinstance(embeddings, torch.Tensor):
                embeddings = torch.tensor(embeddings)

            emb_anchor, emb_positive, emb_negative = embeddings[0], embeddings[1], embeddings[2]

            # Vypočítáme podobnosti
            pos_sim = torch.nn.functional.cosine_similarity(emb_anchor.unsqueeze(0), emb_positive.unsqueeze(0),
                                                            dim=1).item()
            neg_sim = torch.nn.functional.cosine_similarity(emb_anchor.unsqueeze(0), emb_negative.unsqueeze(0),
                                                            dim=1).item()

            # Obtížnost = jak moc je negativní podobný anchoru oproti positivu
            # Normalizace do rozsahu 0-1
            raw_difficulty = max(0, neg_sim - pos_sim + 1) / 2
            return min(1.0, max(0.0, raw_difficulty))

        except Exception as e:
            self.logger.warning(f"Chyba při výpočtu obtížnosti tripletu: {e}")
            return 0.5  # Default střední obtížnost

    def calculate_iteration_difficulty(self, triplets: List[Tuple[str, str, str]]) -> float:
        """
        Spočítá průměrnou obtížnost tripletů v iteraci
        """
        if not triplets:
            return 0.0

        difficulties = []
        for anchor, positive, negative in triplets:
            difficulty = self.calculate_triplet_difficulty(anchor, positive, negative)
            difficulties.append(difficulty)

        avg_difficulty = sum(difficulties) / len(difficulties)
        self.logger.info(f"Průměrná obtížnost iterace: {avg_difficulty:.3f}")
        return avg_difficulty

    def group_triplets_by_difficulty(self, triplets: List[TripletInfo],
                                     n_groups: int = 3) -> List[List[TripletInfo]]:
        """
        Seskupí triplety podle obtížnosti pro curriculum learning

        Args:
            triplets: Seznam tripletů s metadata
            n_groups: Počet skupin obtížnosti

        Returns:
            Seznam skupin tripletů (od nejtěžších po nejlehčí)
        """
        if not triplets:
            return []

        # Seřadíme podle obtížnosti (nejtěžší první)
        sorted_triplets = sorted(triplets, key=lambda x: x.difficulty, reverse=True)

        # Rozdělíme do skupin
        group_size = len(sorted_triplets) // n_groups
        groups = []

        for i in range(n_groups):
            start_idx = i * group_size
            end_idx = start_idx + group_size if i < n_groups - 1 else len(sorted_triplets)
            groups.append(sorted_triplets[start_idx:end_idx])

        self.logger.info(f"Triplety rozděleny do {len(groups)} skupin podle obtížnosti")
        for i, group in enumerate(groups):
            if group:
                avg_diff = sum(t.difficulty for t in group) / len(group)
                self.logger.info(f"  Skupina {i + 1}: {len(group)} tripletů, průměrná obtížnost: {avg_diff:.3f}")

        return groups

    def get_iteration_stats(self, triplets: List[TripletInfo]) -> IterationStats:
        """
        Vypočítá statistiky iterace pro monitoring a debugging
        """
        if not triplets:
            return IterationStats(0, 0.0, {}, (0.0, 0.0), {})

        difficulties = [t.difficulty for t in triplets]
        class_counts = defaultdict(int)
        threshold_stats = {
            'below_pos_threshold': 0,
            'above_neg_threshold': 0,
            'hard_triplets': 0
        }

        for t in triplets:
            class_counts[t.anchor_class] += 1
            if t.positive_score < self.positive_threshold:
                threshold_stats['below_pos_threshold'] += 1
            if t.negative_score > self.negative_threshold:
                threshold_stats['above_neg_threshold'] += 1
            if t.difficulty > 0.7:
                threshold_stats['hard_triplets'] += 1

        return IterationStats(
            total_triplets=len(triplets),
            avg_difficulty=sum(difficulties) / len(difficulties),
            class_distribution=dict(class_counts),
            difficulty_range=(min(difficulties), max(difficulties)),
            threshold_stats=threshold_stats
        )


def generate_triplets_for_iteration(classifier, class_data: Dict[str, List[str]],
                                    positive_threshold: float = 0.8,
                                    negative_threshold: float = 0.65,
                                    top_n: int = 5) -> Tuple[List[Tuple[str, str, str]], Dict]:
    """
    Hlavní funkce pro generování tripletů pro jednu iteraci

    Args:
        classifier: SBertClassifier instance
        class_data: Slovník {třída: [texty]}
        positive_threshold: Práh pro pozitivní příklady
        negative_threshold: Práh pro negativní příklady
        top_n: Počet nejhorších příkladů na třídu

    Returns:
        Tuple[seznam_tripletů, metadata_iterace]
    """
    logger = logging.getLogger(__name__)
    logger.info(f"Generuji triplety pro {len(class_data)} tříd...")

    # Vytvoříme TripletCreator
    creator = TripletCreator(classifier, positive_threshold, negative_threshold)

    # Spočítáme skóre
    scored_data = creator.calculate_scores(class_data)

    # Vytvoříme triplety s metadata
    triplets_info = creator.create_triplets_advanced(scored_data, class_data, top_n)

    # Převedeme na původní formát
    triplets = [(t.anchor, t.positive, t.negative) for t in triplets_info]

    # Spočítáme statistiky
    stats = creator.get_iteration_stats(triplets_info)

    # Metadata pro návrat
    metadata = {
        'stats': stats,
        'avg_difficulty': creator.calculate_iteration_difficulty(triplets),
        'thresholds': {
            'positive': positive_threshold,
            'negative': negative_threshold
        },
        'triplet_count': len(triplets)
    }

    logger.info(f"Vygenerovano {len(triplets)} tripletů s průměrnou obtížností {metadata['avg_difficulty']:.3f}")

    return triplets, metadata


# Ukázkové použití
if __name__ == "__main__":
    from SBertClassifier import SBertClassifier

    # Příklad použití
    classifier = SBertClassifier()

    # Dummy data
    test_data = {
        "otázky_cena": ["Kolik to stojí?", "Jaká je cena?", "Cena produktu"],
        "otázky_čas": ["Kdy zavíráte?", "Otevírací doba", "Kdy máte otevřeno?"]
    }

    # Generování tripletů
    triplets, metadata = generate_triplets_for_iteration(
        classifier, test_data,
        positive_threshold=0.8,
        negative_threshold=0.65
    )

    print(f"Vygenerovano {len(triplets)} tripletů")
    print(f"Metadata: {metadata}")

    if triplets:
        print(f"Příklad tripletu: {triplets[0]}")