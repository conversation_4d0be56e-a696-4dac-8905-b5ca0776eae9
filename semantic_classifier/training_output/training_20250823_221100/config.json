{"base_model": "sentence-transformers/paraphrase-multilingual-mpnet-base-v2", "data_path": "./training_data/training_set.xlsx", "output_dir": "./training_output", "model_dir": "./model", "device": "cpu", "positive_threshold": 0.8, "negative_threshold": 0.65, "top_n": 5, "max_iterations": 15, "max_triplets_per_iteration": 100, "min_improvement_threshold": 0.005, "training_params": {"warmup_steps": 100, "evaluation_steps": 500, "checkpoint_steps": 500, "weight_decay": 0.01, "adam_epsilon": 1e-06}}