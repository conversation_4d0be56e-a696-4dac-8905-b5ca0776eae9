import os
import json
import pandas as pd
import numpy as np
from semantic_classifier.training_data_preparation import load_training_data_from_xlsx
from SBertClassifier import SBertClassifier
def generate_triplet_dataset(xlsx_path):
    class_info, training_data = load_training_data_from_xlsx(xlsx_path)
    triplets = []

    for item in training_data:
        anchor = item['anchor_triplet']
        positives = item['positives_variants']
        negatives = item['negatives']

        n_pos = len(positives)
        n_neg = len(negatives)
        max_count = max(n_pos, n_neg)

        # Vytvoříme všechny kombinace každý s každým
        pairs = [(p, n) for p in positives for n in negatives]

        if max_count < 5:
            selected_pairs = pairs
        elif max_count <= 15:
            selected_pairs = pairs[:100]
        else:
            selected_pairs = pairs[:200]

        for positive, negative in selected_pairs:
            triplets.append({
                'anchor': anchor,
                'positive': positive,
                'negative': negative
            })

    df = pd.DataFrame(triplets)
    return df

def generate_adaptive_triplet_dataset(
    training_data,
    model_dir='fine_tuned_sbert',
    target_pos_threshold=0.8,
    target_neg_threshold=0.7,
    max_iterations=6,
    pairs_per_iteration=6,
    verbose=True,
    reset_model=False
):
    """
    Iterativní generování tripletů s adaptivními parametry a skutečným early stopping.

    1. Najdi TOP-K nejtěžších případů (nejmenší pos, největší neg skóre)
    2. Trénuj s agresivními parametry
    3. Postupně zjemňuj parametry
    4. Opakuj dokud nejsou splněny prahy
    """
    all_triplets = []

    # Reset model na začátku pokud je požadováno
    if reset_model:
        if verbose:
            print("🔄 Resetuji model na původní stav...")
        from semantic_classifier.fine_tune_sbert import prepare_sentence_transformer
        import os
        if os.path.exists(model_dir):
            import shutil
            shutil.rmtree(model_dir)
        # Vytvoř čistý model
        model = prepare_sentence_transformer('sentence-transformers/paraphrase-multilingual-mpnet-base-v2', 'cpu')
        model.save(model_dir)

    for iteration in range(max_iterations):
        if verbose:
            print(f"\n🔄 Iterace {iteration+1}/{max_iterations}")

        # Vyčisti paměť na začátku iterace
        import gc
        import torch
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            torch.mps.empty_cache()

        # 1. Vyhodnoť současný stav
        hard_cases = find_hardest_cases(training_data, model_dir,
                                       target_pos_threshold, target_neg_threshold,
                                       pairs_per_iteration, verbose)

        if not hard_cases or len(hard_cases) < 3:
            if verbose:
                print(f"✅ Všechny případy splňují prahy po {iteration} iteracích (zbývá {len(hard_cases)} problémových tříd)")
            break

        # 2. Adaptivní parametry podle iterace
        params = get_adaptive_params(iteration, max_iterations)

        # 3. Generuj triplets jen pro hard cases
        iteration_triplets = generate_targeted_triplets(hard_cases, params, model_dir, verbose)

        if verbose:
            print(f"   📊 {len(hard_cases)} hard cases, {len(iteration_triplets)} tripletů, "
                  f"margin={params['triplet_margin']:.2f}")

        # 4. Trénuj model s novými triplety
        if iteration_triplets:
            train_iteration_model(iteration_triplets, params, model_dir, iteration, verbose)

        all_triplets.extend(iteration_triplets)

    if verbose:
        print(f"\n🎯 Celkem vygenerováno {len(all_triplets)} tripletů přes {iteration+1} iterací")
        print(f"🏁 Model byl trénován iterativně, finální verze uložena v: {model_dir}")

    return all_triplets

def train_iteration_model(triplets, params, model_dir, iteration, verbose=True):
    """Trénuj model s triplety z jedné iterace s early stopping."""
    import pandas as pd
    from sentence_transformers import SentenceTransformer, InputExample, losses
    from sentence_transformers.evaluation import TripletEvaluator
    import os
    import numpy as np

    if verbose:
        print(f"   🚂 Trénuji model s {len(triplets)} triplety (iterace {iteration+1})")

    # Převeď triplets na DataFrame
    df = pd.DataFrame(triplets)

    # Načti model
    model = SentenceTransformer(model_dir)

    # Vytvoř tréninková data
    train_examples = []
    for _, row in df.iterrows():
        train_examples.append(InputExample(
            texts=[row['anchor'], row['positive'], row['negative']]
        ))

    # Definuj loss funkci s adaptivním margin
    train_loss = losses.TripletLoss(
        model=model,
        distance_metric=losses.TripletDistanceMetric.COSINE,
        triplet_margin=params['triplet_margin']
    )

    # Optimalizované epochy a learning rate pro early stopping
    max_epochs = max(5, 12 - iteration)  # 12→11→10→9→8→7→6→5 epoch (více času)
    learning_rate = 2e-5 * (0.92 ** iteration)  # Pomalejší pokles learning rate

    # Vytvoř evaluátor pro early stopping (použij menší část pro evaluaci)
    # Vezmi pouze 15% dat pro evaluaci, zbytek pro trénink
    split_idx = int(len(train_examples) * 0.85)
    actual_train_examples = train_examples[:split_idx]
    eval_examples = train_examples[split_idx:]

    if len(eval_examples) < 5:  # Pokud je málo dat, použij minimálně 5 příkladů
        eval_examples = train_examples[-min(8, len(train_examples)//3):]
        actual_train_examples = train_examples[:-len(eval_examples)]

    evaluator = TripletEvaluator.from_input_examples(eval_examples, name='iteration_eval')

    if verbose:
        print(f"      → max {max_epochs} epoch, lr={learning_rate:.2e}, margin={params['triplet_margin']:.3f}")
        print(f"      → train: {len(actual_train_examples)} examples, eval: {len(eval_examples)} examples")

    # Použij DataLoader pro trénink s menším batch_size pro úsporu paměti
    from torch.utils.data import DataLoader

    batch_size = max(4, min(8, len(actual_train_examples) // 4))  # Adaptivní batch_size 4-8
    train_dataloader = DataLoader(actual_train_examples, shuffle=True, batch_size=batch_size)

    # Implementuj vlastní early stopping s optimalizovanými parametry
    best_score = -1
    patience = 4  # Zvýšená patience pro stabilnější trénink
    patience_counter = 0
    min_improvement = 0.01  # Minimální zlepšení pro reset patience

    for epoch in range(max_epochs):
        if verbose:
            print(f"        Epoch {epoch+1}/{max_epochs}")

        # Trénuj jednu epochu
        model.fit(
            train_objectives=[(train_dataloader, train_loss)],
            epochs=1,
            warmup_steps=0 if epoch > 0 else min(5, len(actual_train_examples) // 8),
            optimizer_params={'lr': learning_rate},
            show_progress_bar=False,  # Vypni progress bar pro čistší výstup
            output_path=None
        )

        # Vlastní evaluace - spočítej accuracy na eval datech
        correct = 0
        total = len(eval_examples)

        for example in eval_examples:
            anchor_emb = model.encode(example.texts[0])
            pos_emb = model.encode(example.texts[1])
            neg_emb = model.encode(example.texts[2])

            # Cosine similarity
            from sklearn.metrics.pairwise import cosine_similarity
            import numpy as np

            pos_sim = cosine_similarity([anchor_emb], [pos_emb])[0][0]
            neg_sim = cosine_similarity([anchor_emb], [neg_emb])[0][0]

            # Správně klasifikováno pokud pozitivní > negativní
            if pos_sim > neg_sim:
                correct += 1

        current_accuracy = correct / total if total > 0 else 0.0

        if verbose:
            print(f"        → Accuracy: {current_accuracy:.3f} ({correct}/{total})")

        # Optimalizovaná early stopping logika
        if current_accuracy > best_score + min_improvement:
            best_score = current_accuracy
            patience_counter = 0
            if verbose:
                print(f"        📈 Nové nejlepší skóre: {current_accuracy:.3f}")
        else:
            patience_counter += 1
            if verbose:
                print(f"        ⏳ Patience: {patience_counter}/{patience}")

        if patience_counter >= patience:
            if verbose:
                print(f"        ⏹️ Early stopping po {epoch+1} epochách (patience={patience}, best={best_score:.3f})")
            break

    if verbose and patience_counter < patience:
        print(f"        ✅ Dokončeno {max_epochs} epoch bez early stopping")

    # Ulož model zpět
    model.save(model_dir)

    # Vyčisti paměť po tréninku
    del model
    del train_examples
    del train_dataloader
    del train_loss

    import gc
    import torch
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
        torch.mps.empty_cache()

    if verbose:
        print(f"      ✅ Model aktualizován, uložen a paměť vyčištěna")


def find_hardest_cases(training_data, model_dir, pos_thresh, neg_thresh, top_k, verbose=True):
    """Najdi nejtěžší případy pro každou třídu."""
    scorer = SBertClassifier(model_dir=model_dir)
    hard_cases = []
    total_hard_pos = 0
    total_hard_neg = 0

    for item in training_data:
        anchor = item['anchor_triplet']
        positives = item['positives_variants']
        negatives = item['negatives']
        category_name = item['category_name']

        # Najdi nejhorší pozitiva (pod prahem)
        pos_scores = [(p, scorer.similarity(anchor, p)) for p in positives]
        worst_positives = [(p, s) for p, s in pos_scores if s < pos_thresh]
        worst_positives.sort(key=lambda x: x[1])  # nejnižší skóre první

        # Najdi nejhorší negativa (nad prahem)
        neg_scores = [(n, scorer.similarity(anchor, n)) for n in negatives]
        worst_negatives = [(n, s) for n, s in neg_scores if s > neg_thresh]
        worst_negatives.sort(key=lambda x: x[1], reverse=True)  # nejvyšší skóre první

        if worst_positives or worst_negatives:
            hard_cases.append({
                'category_name': category_name,
                'anchor_triplet': anchor,
                'hard_positives': [p for p, s in worst_positives[:top_k]],
                'hard_negatives': [n for n, s in worst_negatives[:top_k]],
                'all_positives': positives,
                'all_negatives': negatives
            })
            total_hard_pos += len(worst_positives[:top_k])
            total_hard_neg += len(worst_negatives[:top_k])

            if verbose:
                print(f"   🎯 {category_name}: {len(worst_positives[:top_k])} hard pos, "
                      f"{len(worst_negatives[:top_k])} hard neg")

    if verbose:
        print(f"   📈 Celkem {len(hard_cases)} tříd s problémy, "
              f"{total_hard_pos} hard pozitiv, {total_hard_neg} hard negativ")

    return hard_cases


def get_adaptive_params(iteration, max_iterations):
    """Optimalizované adaptivní parametry pro early stopping."""
    progress = iteration / max(1, max_iterations - 1)  # 0.0 → 1.0

    return {
        'triplet_margin': 0.75 - (progress * 0.20),  # 0.75 → 0.55 (konzervativnější)
        'easy_pos_ratio': 0.75 - (progress * 0.15),  # 0.75 → 0.60
        'easy_pos_offset': 0.65 - (progress * 0.05), # 0.65 → 0.60
        'easy_neg_ratio': 0.85 - (progress * 0.10),  # 0.85 → 0.75
        'easy_neg_offset': 0.65 - (progress * 0.05), # 0.65 → 0.60
        'min_pairs_per_class': max(8, int(20 - iteration * 1.0))  # 20 → 8 (pomalejší pokles)
    }


def generate_targeted_triplets(hard_cases, params, model_dir, verbose=True):
    """Generuj triplets cíleně pro hard cases s danými parametry."""
    triplets = []
    scorer = SBertClassifier(model_dir=model_dir)

    for case in hard_cases:
        anchor = case['anchor_triplet']
        category_name = case['category_name']

        # Kombinuj hard případy s normálními pro kontext
        positives = case['hard_positives'] + case['all_positives']
        negatives = case['hard_negatives'] + case['all_negatives']

        # Odstraň duplikáty, zachovej pořadí (hard první)
        seen_pos = set()
        unique_positives = []
        for p in positives:
            if p not in seen_pos:
                unique_positives.append(p)
                seen_pos.add(p)

        seen_neg = set()
        unique_negatives = []
        for n in negatives:
            if n not in seen_neg:
                unique_negatives.append(n)
                seen_neg.add(n)

        # Generuj triplets s adaptivními parametry
        case_triplets = generate_triplets_for_class(
            anchor, category_name, unique_positives, unique_negatives,
            scorer, params
        )

        triplets.extend(case_triplets)

        if verbose and case_triplets:
            print(f"      → {category_name}: {len(case_triplets)} tripletů")

    return triplets


def generate_triplets_for_class(anchor, category_name, positives, negatives, scorer, params):
    """Generuj triplets pro jednu třídu s danými parametry."""
    triplets = []
    used_pos = set()
    used_neg = set()

    # Oskóruj všechny kandidáty
    scored_positives = [(p, scorer.similarity(anchor, p)) for p in positives]
    scored_negatives = [(n, scorer.similarity(anchor, n)) for n in negatives]

    # Seřaď podle skóre
    scored_positives.sort(key=lambda x: x[1], reverse=True)
    scored_negatives.sort(key=lambda x: x[1])

    pairs_generated = 0
    min_pairs = params['min_pairs_per_class']

    # Generuj páry dokud nedosáhneme min_pairs nebo nevyčerpáme kandidáty
    while pairs_generated < min_pairs and scored_positives and scored_negatives:
        # Vyber pozitiv - preferuj easy pozitiva s vysokým skóre
        pos_cand = [(p, s) for p, s in scored_positives if p not in used_pos]
        if not pos_cand:
            break

        # Easy pozitiva - vysoké skóre
        easy_pos_count = max(1, int(params['easy_pos_ratio'] * len(pos_cand)))
        easy_pos_threshold = max(0.5, params['easy_pos_offset'])
        easy_pos = [(p, s) for p, s in pos_cand if s >= easy_pos_threshold][:easy_pos_count]

        if easy_pos:
            positive, pos_score = easy_pos[0]
        else:
            positive, pos_score = pos_cand[0]

        # Vyber negativ - preferuj easy negativa s nízkým skóre
        neg_cand = [(n, s) for n, s in scored_negatives if n not in used_neg]
        if not neg_cand:
            break

        # Easy negativa - nízké skóre
        easy_neg_count = max(1, int(params['easy_neg_ratio'] * len(neg_cand)))
        easy_neg_threshold = min(0.5, params['easy_neg_offset'])
        easy_neg = [(n, s) for n, s in neg_cand if s <= easy_neg_threshold][:easy_neg_count]

        if easy_neg:
            negative, neg_score = easy_neg[0]
        else:
            negative, neg_score = neg_cand[0]

        # Vytvoř triplet
        triplets.append({
            'anchor': anchor,
            'positive': positive,
            'negative': negative,
            'category': category_name
        })

        # Označ jako použité
        used_pos.add(positive)
        used_neg.add(negative)
        pairs_generated += 1

    return triplets


def generate_smart_triplet_dataset(
    xlsx_path,
    positive_threshold=None,
    negative_threshold=None,
    model_dir='fine_tuned_sbert',
    margin_zone=0.1,
    expand_step=0.03,
    max_rounds=12,
    batch_per_round=None,
    easy_neg_ratio=0.7,
    easy_pos_ratio=0.5,
    easy_pos_offset=0.45,

    easy_neg_offset=0.45,
    min_pairs_per_class=12,
):
    """
    Vytvoří 'smart' triplet dataset z tréninkového XLSX s iterativním vyčerpáním kandidátů.
    - Načte decision threshold T z model_dir/best_threshold.json (pokud existuje)
    - Kolem T tvoří okno [T, T+dz] pro hard-pozitivy a [T-dz, T) pro hard-negativy
    - Iterativně rozšiřuje okno o expand_step, dokud se nevyčerpají kandidáti nebo max_rounds
    - Přimíchává easy-negativy hluboko pod T (easy_neg_ratio)
    """
    class_info, training_data = load_training_data_from_xlsx(xlsx_path)
    triplets = []
    scorer = SBertClassifier()
    triplets_per_class = {}

    # Základní práh
    T_base = None
    try:
        p = os.path.join(model_dir, 'best_threshold.json')
        with open(p, 'r', encoding='utf-8') as f:
            data = json.load(f)
            T_base = float(data.get('threshold', 0.6))
            # Opatrné omezení do [0,1]
            T_base = max(0.0, min(0.99, T_base))
    except Exception:
        T_base = 0.6

    dz0 = max(0.01, float(margin_zone))
    step = max(0.0, float(expand_step))
    # Připrav sousední třídy podle prototypů (pokud jsou dostupné)
    neighbor_classes: dict = {}
    try:
        # Vytvoř mapu kategorie -> reprezentativní texty (kotva + první pozitiv)
        class_texts = {}
        for item in training_data:
            cname = item.get('category_name', 'Unknown')
            texts = [item['anchor_triplet']]
            if item['positives_variants']:
                texts.append(item['positives_variants'][0])
            class_texts[cname] = texts
        # Spočítej prototypy v paměti (bez ukládání na disk)
        tmp = SBertClassifier(model_dir=model_dir)
        names = list(class_texts.keys())
        proto_list = []
        for cname in names:
            embeddings = tmp.encode(class_texts[cname])
            centroid = embeddings.mean(axis=0)
            norm = np.linalg.norm(centroid)
            if norm > 0:
                centroid = centroid / norm
            proto_list.append(centroid.astype(np.float32))
        if proto_list:
            proto = np.stack(proto_list, axis=0)  # (C, D)
            # Kosinusová podobnost mezi prototypy: sim = P @ P^T (P je již normalizovaná)
            sim_mat = np.dot(proto, proto.T)
            for i, name in enumerate(names):
                sims = sim_mat[i]
                # Seřaď kromě sebe sama a vezmi top 3 sousedy
                order = np.argsort(-sims)
                neighbors = [names[j] for j in order if j != i][:3]
                neighbor_classes[name] = set(neighbors)
    except Exception as e:
        print(f"⚠️ Nepodařilo se vytvořit sousední třídy: {e}")
        neighbor_classes = {}

    print(f"📏 generate_smart_triplet_dataset: T={T_base:.3f}, dz0={dz0:.3f}, step={step:.3f}, rounds≤{max_rounds}")

    for item in training_data:
        anchor = item['anchor_triplet']
        positives = item['positives_variants']
        negatives = item['negatives']
        category_name = item.get('category_name', 'Neznámá kategorie')

        # Oskórování
        scored_positives = [(p, scorer.similarity(anchor, p)) for p in positives]
        scored_negatives = [(n, scorer.similarity(anchor, n)) for n in negatives]

        # Pokud máme sousední třídy, setřiď scored_negatives tak, aby šly přednostně ze sousedů
        if neighbor_classes and category_name in neighbor_classes:
            neighbors = neighbor_classes.get(category_name, set())
            if neighbors:
                def is_from_neighbors(text):
                    try:
                        cid, _ = scorer.classify(text)
                        return scorer.get_class_name(cid) in neighbors
                    except Exception:
                        return False
                scored_negatives.sort(key=lambda x: (not is_from_neighbors(x[0]), -x[1]))

        used_pos = set()
        used_neg = set()
        class_pairs = []

        for r in range(int(max_rounds)):
            dz = dz0 + r * step
            # Okno relativně k T s postupným rozšiřováním
            pos_low, pos_high = T_base, min(1.0, T_base + dz)
            neg_low, neg_high = max(0.0, T_base - dz), T_base

            # Kandidáti v aktuálním okně, kteří ještě nebyli použiti
            pos_cand = [(p, s) for p, s in scored_positives if (p not in used_pos) and (pos_low <= s <= pos_high)]
            neg_cand = [(n, s) for n, s in scored_negatives if (n not in used_neg) and (neg_low <= s < neg_high)]

            # Seřazení: těžší první (blíž T)
            # Přimíchej easy-pozitivy výrazně nad T
            k_ep = max(0, int(easy_pos_ratio * len(pos_cand)))
            if k_ep > 0:
                easy_pos_cand = [(p, s) for p, s in scored_positives if (p not in used_pos) and (s > min(1.0, T_base + easy_pos_offset))]
                easy_pos_cand.sort(key=lambda x: x[1], reverse=True)
                pos_cand = (pos_cand[:max(1, len(pos_cand) - k_ep)] + easy_pos_cand[:k_ep]) or pos_cand

            pos_cand.sort(key=lambda x: x[1])
            neg_cand.sort(key=lambda x: x[1], reverse=True)

            # Přimíchej easy-negativy hluboko pod T
            k_e = max(0, int(easy_neg_ratio * len(neg_cand)))
            if k_e > 0:
                easy_cand = [(n, s) for n, s in scored_negatives if (n not in used_neg) and (s < max(0.0, T_base - easy_neg_offset))]
                easy_cand.sort(key=lambda x: x[1])
                neg_cand = (neg_cand[:max(1, len(neg_cand) - k_e)] + easy_cand[:k_e]) or neg_cand

            # Pokud nejsou noví kandidáti, pokračuj do dalšího kola (okno se rozšíří)
            if not pos_cand or not neg_cand:
                continue

            # Kolik párů spárujeme v tomto kole
            pair_count = min(len(pos_cand), len(neg_cand))
            if batch_per_round is not None:
                pair_count = min(pair_count, int(batch_per_round))

            selected_pairs = list(zip([p for p, _ in pos_cand[:pair_count]], [n for n, _ in neg_cand[:pair_count]]))

            # Zaznamenej a označ použité
            for p, n in selected_pairs:
                if p not in used_pos and n not in used_neg:
                    class_pairs.append((p, n))
                    used_pos.add(p)
                    used_neg.add(n)
            # Pokud po rozšíření okna stále nic, přidej fallbacky z nejbližších hodnot k T
            if not class_pairs and r == int(max_rounds) - 1:
                # vyber 3 nejbližší nad T jako pozitivy a 3 nejbližší pod T jako negativy
                above = sorted([(p, s) for p, s in scored_positives if p not in used_pos], key=lambda x: abs(x[1]-T_base))[:3]
                below = sorted([(n, s) for n, s in scored_negatives if n not in used_neg], key=lambda x: abs(x[1]-T_base))[:3]
                k = min(len(above), len(below))
                for i in range(k):
                    p, _ = above[i]
                    n, _ = below[i]
                    class_pairs.append((p, n))
                    used_pos.add(p)
                    used_neg.add(n)


        # Pokud i po všech kolech je málo párů, doplň nejbližšími k T (vyčerpávací fallback)
        if len(class_pairs) < min_pairs_per_class:
            remain_p = [p for p, _ in sorted([(p, s) for p, s in scored_positives if p not in used_pos], key=lambda x: abs(x[1]-T_base))]
            remain_n = [n for n, _ in sorted([(n, s) for n, s in scored_negatives if n not in used_neg], key=lambda x: abs(x[1]-T_base))]
            k = min(min_pairs_per_class - len(class_pairs), len(remain_p), len(remain_n))
            for i in range(k):
                class_pairs.append((remain_p[i], remain_n[i]))
                used_pos.add(remain_p[i])
                used_neg.add(remain_n[i])

            # Heuristika: pokud jsme použili všechny dostupné v tomto okně a nejsou další, cyklus pokračuje rozšířením okna

        # Ulož triplet páry pro třídu
        triplets_per_class[category_name] = len(class_pairs)
        for positive, negative in class_pairs:
            triplets.append({'anchor': anchor, 'positive': positive, 'negative': negative})

    # Statistiky
    print("\n=== STATISTIKY TRIPLETŮ PRO KAŽDOU TŘÍDU ===")
    total_triplets = 0
    for category_name, count in triplets_per_class.items():
        print(f"  {category_name}: {count} tripletů")
        total_triplets += count
    print(f"\nCelkem vytvořeno {total_triplets} tripletů z {len(triplets_per_class)} tříd.")
    print("=" * 50)

    df = pd.DataFrame(triplets)
    return df

