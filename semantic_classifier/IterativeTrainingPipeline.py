import json
import os
import shutil
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import pandas as pd
import logging
from dataclasses import asdict
from sentence_transformers import SentenceTransformer

# Import v<PERSON><PERSON><PERSON> modulů
from training_data_preparation import load_training_data_from_xlsx, preprocess_text
from SBertClassifier import SBertClassifier
from fine_tune_script import train_model, get_adaptive_params
from TripletCreator import generate_triplets_for_iteration, TripletCreator


class IterativeTrainingPipeline:
    """
    Hlavní třída pro iterativní trénink modelu s curriculum learning
    """

    def __init__(self, config_path: str):
        self.config = self.load_config(config_path)
        self.classifier = None
        self.positive_examples = None
        self.negative_examples = None
        self.class_info = None
        self.used_triplets = set()
        self.iteration_history = []

        # Nastavení loggeru
        self.setup_logging()
        self.logger = logging.getLogger(__name__)

    def setup_logging(self):
        """Nastavuje logging pro celou pipeline"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('iterative_training.log', encoding='utf-8')
            ]
        )

    def load_config(self, config_path: str) -> Dict:
        """Načte konfigurační soubor s validací"""
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"Konfigurační soubor {config_path} nenalezen")

        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)

        # Validace povinných klíčů
        required_keys = ['data_path', 'output_dir', 'model_dir']
        for key in required_keys:
            if key not in config:
                raise ValueError(f"Povinný klíč '{key}' chybí v konfiguraci")

        return config

    def load_data(self):
        """Načte data z Excelu pomocí vaší funkce"""
        self.logger.info("Načítám tréninková data...")

        self.positive_examples, self.negative_examples, self.class_info = load_training_data_from_xlsx(
            self.config['data_path']
        )

        if not self.positive_examples:
            raise ValueError("Žádná tréninková data nebyla načtena")

        # Statistiky dat
        self.logger.info("Statistiky tříd:")
        total_pos = 0
        total_neg = 0

        for class_name, examples in self.positive_examples.items():
            pos_count = len(examples)
            neg_count = len(self.negative_examples.get(class_name, []))
            total_pos += pos_count
            total_neg += neg_count

            self.logger.info(f"  {class_name}: {pos_count} pozitivních, {neg_count} negativních příkladů")

        self.logger.info(
            f"Celkem: {total_pos} pozitivních, {total_neg} negativních příkladů napříč {len(self.positive_examples)} třídami")

    def initialize_model(self):
        """Inicializuje model a připraví prototypy"""
        self.logger.info("Inicializuji model...")

        # Inicializace SBertClassifier
        self.classifier = SBertClassifier(
            model_name=self.config.get('base_model', 'sentence-transformers/paraphrase-multilingual-mpnet-base-v2'),
            model_dir=self.config.get('model_dir', './model'),
            device=self.config.get('device', 'cpu')
        )

        # Vytvoření počátečních prototypů pro evaluaci
        self.logger.info("Vytvářím počáteční prototypy...")
        self.classifier.create_and_save_prototypes(self.positive_examples)

        self.logger.info(f"Model inicializován na zařízení: {self.classifier.device}")

    def prepare_class_data(self) -> Dict[str, List[str]]:
        """
        Připraví kombinovaná data pro každou třídu (pozitivní + vzorky negativních)

        Returns:
            Slovník {class_name: [všechny_relevantní_texty]}
        """
        class_data = {}

        for class_name in self.positive_examples.keys():
            # Začneme s pozitivními příklady
            all_examples = self.positive_examples[class_name].copy()

            # Přidáme vlastní negativní příklady (pokud existují)
            if class_name in self.negative_examples:
                all_examples.extend(self.negative_examples[class_name])

            # Přidáme vzorky negativních příkladů z jiných tříd pro lepší kontrastování
            other_negatives = []
            for other_class, other_positives in self.positive_examples.items():
                if other_class != class_name:
                    # Přidáme první 2-3 příklady z každé jiné třídy jako negativní
                    sample_size = min(3, len(other_positives))
                    other_negatives.extend(other_positives[:sample_size])

            # Omezíme počet negativních příkladů, aby nedominovali
            max_negatives = len(self.positive_examples[class_name]) * 2
            all_examples.extend(other_negatives[:max_negatives])

            class_data[class_name] = all_examples

        return class_data

    def evaluate_current_performance(self) -> Dict:
        """
        Evaluuje aktuální výkon modelu na všech datech

        Returns:
            Slovník s metrikami výkonu
        """
        self.logger.info("Evaluuji aktuální výkon modelu...")

        # Aktualizujeme prototypy
        self.classifier.create_and_save_prototypes(self.positive_examples)

        # Testujeme na pozitivních příkladech
        correct = 0
        total = 0
        class_accuracy = {}

        for class_name, examples in self.positive_examples.items():
            class_correct = 0
            for example in examples:
                predicted_id, confidence = self.classifier.classify(example)
                predicted_name = self.classifier.get_class_name(predicted_id)

                if predicted_name == class_name:
                    correct += 1
                    class_correct += 1
                total += 1

            class_accuracy[class_name] = class_correct / len(examples) if examples else 0.0

        overall_accuracy = correct / total if total > 0 else 0.0

        performance = {
            'overall_accuracy': overall_accuracy,
            'class_accuracy': class_accuracy,
            'total_examples': total,
            'correct_predictions': correct
        }

        self.logger.info(f"Celková přesnost: {overall_accuracy:.3f}")
        self.logger.info(f"Třídní přesnosti: {class_accuracy}")

        return performance

    def select_iteration_triplets(self, all_triplets: List[Tuple], max_triplets_per_iter: int = 100) -> List[Tuple]:
        """
        Vybere triplety pro aktuální iteraci podle curriculum learning strategie

        Args:
            all_triplets: Všechny dostupné triplety
            max_triplets_per_iter: Maximální počet tripletů na iteraci

        Returns:
            Seznam tripletů pro aktuální iteraci
        """
        # Odfiltrujeme již použité triplety
        new_triplets = []
        for triplet in all_triplets:
            triplet_key = tuple(triplet)  # Ensure it's hashable
            if triplet_key not in self.used_triplets:
                new_triplets.append(triplet)

        # Omezíme počet tripletů na iteraci
        if len(new_triplets) > max_triplets_per_iter:
            new_triplets = new_triplets[:max_triplets_per_iter]

        # Označíme triplety jako použité
        for triplet in new_triplets:
            self.used_triplets.add(tuple(triplet))

        return new_triplets

    def run_iteration(self, iteration: int, output_dir: str) -> Tuple[bool, Dict]:
        """
        Provede jednu iteraci tréninku s kompletním tracking

        Args:
            iteration: Číslo iterace
            output_dir: Výstupní adresář

        Returns:
            Tuple[success, iteration_results]
        """
        iteration_dir = os.path.join(output_dir, f"iteration_{iteration:02d}")
        os.makedirs(iteration_dir, exist_ok=True)

        self.logger.info(f"\n{'=' * 50}")
        self.logger.info(f"Iterace {iteration}")
        self.logger.info(f"{'=' * 50}")

        try:
            # Evaluace před tréninkém
            pre_performance = self.evaluate_current_performance()

            # Připravíme class data
            class_data = self.prepare_class_data()

            # Generování tripletů
            self.logger.info("Generuji triplety...")
            triplets, triplet_metadata = generate_triplets_for_iteration(
                classifier=self.classifier,
                class_data=class_data,
                positive_threshold=self.config.get('positive_threshold', 0.8),
                negative_threshold=self.config.get('negative_threshold', 0.65),
                top_n=self.config.get('top_n', 5)
            )

            if not triplets:
                self.logger.warning("Žádné triplety k trénování. Ukončuji iteraci.")
                return False, {'reason': 'no_triplets'}

            # Výběr tripletů pro iteraci
            iteration_triplets = self.select_iteration_triplets(
                triplets,
                max_triplets_per_iter=self.config.get('max_triplets_per_iteration', 100)
            )

            if not iteration_triplets:
                self.logger.warning("Všechny triplety již byly použity. Ukončuji.")
                return False, {'reason': 'all_triplets_used'}

            # Získání adaptivních parametrů
            difficulty = triplet_metadata['avg_difficulty']
            train_params = get_adaptive_params(difficulty, iteration)

            # Přidáme základní parametry z configu
            base_params = self.config.get('training_params', {})
            train_params.update(base_params)

            self.logger.info(f"Obtížnost iterace: {difficulty:.3f}")
            self.logger.info(f"Počet tripletů: {len(iteration_triplets)}")
            self.logger.info(f"Parametry tréninku: {train_params}")

            # Příprava validačních dat (volitelné)
            val_data = None  # Můžete implementovat split validačních dat

            # Trénink
            self.logger.info("Zahájení tréninku...")

            iteration_info = {
                'iteration': iteration,
                'difficulty': difficulty,
                'num_triplets': len(iteration_triplets),
                'triplet_metadata': triplet_metadata,
                'pre_training_performance': pre_performance
            }

            trained_model, train_results = train_model(
                model=self.classifier.model,
                triplets=iteration_triplets,
                output_path=iteration_dir,
                train_params=train_params,
                val_data=val_data,
                iteration_info=iteration_info
            )

            # Aktualizace modelu v klasifikátoru
            self.classifier.model = trained_model

            # Evaluace po tréninku
            post_performance = self.evaluate_current_performance()

            # Kompletní výsledky iterace
            iteration_results = {
                'iteration': iteration,
                'success': True,
                'difficulty': difficulty,
                'num_triplets': len(iteration_triplets),
                'train_params': train_params,
                'train_results': train_results,
                'pre_training_performance': pre_performance,
                'post_training_performance': post_performance,
                'improvement': post_performance['overall_accuracy'] - pre_performance['overall_accuracy'],
                'triplet_metadata': triplet_metadata,
                'timestamp': datetime.now().isoformat()
            }

            # Uložení výsledků iterace
            with open(os.path.join(iteration_dir, 'iteration_results.json'), 'w', encoding='utf-8') as f:
                json.dump(iteration_results, f, indent=2, ensure_ascii=False)

            # Uložení tripletů pro debugging
            triplets_df = pd.DataFrame(iteration_triplets, columns=['anchor', 'positive', 'negative'])
            triplets_df.to_csv(os.path.join(iteration_dir, 'triplets.csv'), index=False, encoding='utf-8')

            self.iteration_history.append(iteration_results)

            self.logger.info(f"Iterace dokončena. Zlepšení přesnosti: {iteration_results['improvement']:.3f}")

            return True, iteration_results

        except Exception as e:
            self.logger.error(f"Chyba během iterace {iteration}: {e}")
            import traceback
            traceback.print_exc()

            error_results = {
                'iteration': iteration,
                'success': False,
                'error': str(e),
                'traceback': traceback.format_exc(),
                'timestamp': datetime.now().isoformat()
            }

            with open(os.path.join(iteration_dir, 'error.json'), 'w', encoding='utf-8') as f:
                json.dump(error_results, f, indent=2, ensure_ascii=False)

            return False, error_results

    def should_continue_training(self, recent_results: List[Dict], min_improvement: float = 0.01) -> bool:
        """
        Rozhodne, zda pokračovat v tréninku na základě posledních výsledků

        Args:
            recent_results: Seznam posledních výsledků iterací
            min_improvement: Minimální požadované zlepšení

        Returns:
            True pokud má smysl pokračovat
        """
        if len(recent_results) < 3:
            return True  # Pokračuj, dokud nemáme dostatek historie

        # Kontrola posledních 3 iterací
        last_3_improvements = [r.get('improvement', 0) for r in recent_results[-3:]]
        avg_recent_improvement = sum(last_3_improvements) / len(last_3_improvements)

        if avg_recent_improvement < min_improvement:
            self.logger.info(f"Průměrné zlepšení posledních 3 iterací ({avg_recent_improvement:.4f}) "
                             f"je pod prahem ({min_improvement}). Ukončuji trénink.")
            return False

        return True

    def save_final_results(self, output_dir: str):
        """Uloží finální výsledky a model"""
        # Finální evaluace
        final_performance = self.evaluate_current_performance()

        # Shrnutí celého tréninku
        training_summary = {
            'config': self.config,
            'total_iterations': len(self.iteration_history),
            'final_performance': final_performance,
            'iteration_history': self.iteration_history,
            'total_triplets_used': len(self.used_triplets),
            'training_completed_at': datetime.now().isoformat()
        }

        # Uložení shrnutí
        with open(os.path.join(output_dir, 'training_summary.json'), 'w', encoding='utf-8') as f:
            json.dump(training_summary, f, indent=2, ensure_ascii=False)

        # Uložení finálního modelu
        final_model_path = os.path.join(output_dir, "final_model")
        self.classifier.model.save(final_model_path)

        # Uložení finálních prototypů
        self.classifier.create_and_save_prototypes(self.positive_examples)

        # Export pro C++ (volitelné)
        try:
            cpp_export_path = os.path.join(output_dir, "cpp_export")
            self.classifier.export_for_cpp(cpp_export_path, export_model=False)  # Rychlejší bez ONNX
            self.logger.info(f"C++ export vytvořen: {cpp_export_path}")
        except Exception as e:
            self.logger.warning(f"C++ export selhal: {e}")

        self.logger.info(f"Finální model uložen: {final_model_path}")
        self.logger.info(f"Finální přesnost: {final_performance['overall_accuracy']:.3f}")

    def run(self):
        """Hlavní smyčka iterativního tréninku"""
        # Příprava výstupního adresáře
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = os.path.join(self.config['output_dir'], f"training_{timestamp}")
        os.makedirs(output_dir, exist_ok=True)

        self.logger.info(f"Spouštím iterativní trénink. Výstup: {output_dir}")

        try:
            # Uložení konfigurace
            with open(os.path.join(output_dir, 'config.json'), 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)

            # Načtení dat
            self.load_data()

            # Inicializace modelu
            self.initialize_model()

            # Hlavní iterační smyčka
            iteration = 0
            max_iterations = self.config.get('max_iterations', 10)
            successful_results = []

            while iteration < max_iterations:
                success, results = self.run_iteration(iteration, output_dir)

                if success:
                    successful_results.append(results)

                    # Kontrola, zda má smysl pokračovat
                    if not self.should_continue_training(successful_results):
                        break
                else:
                    # Po neúspěšné iteraci můžeme pokračovat nebo skončit
                    reason = results.get('reason', 'unknown_error')
                    if reason in ['no_triplets', 'all_triplets_used']:
                        self.logger.info("Trénink ukončen - vyčerpány dostupné triplety")
                        break
                    # Pro ostatní chyby pokračujeme

                iteration += 1

            # Uložení finálních výsledků
            self.save_final_results(output_dir)

            self.logger.info(f"\nTrénink dokončen po {len(successful_results)} úspěšných iteracích.")
            self.logger.info(f"Výsledky uloženy v: {output_dir}")

        except Exception as e:
            self.logger.error(f"Kritická chyba v hlavní smyčce: {e}")
            import traceback
            traceback.print_exc()
            raise


def create_default_config(config_path: str = "config.json") -> Dict:
    """Vytvoří výchozí konfigurační soubor"""
    config = {
        "base_model": "sentence-transformers/paraphrase-multilingual-mpnet-base-v2",
        "data_path": "./data/training_set.xlsx",
        "output_dir": "./training_output",
        "model_dir": "./model",
        "device": "cpu",  # Změňte na "cuda" pokud máte GPU

        # Threshold parametry pro triplet mining
        "positive_threshold": 0.8,
        "negative_threshold": 0.65,
        "top_n": 5,

        # Parametry iterativního tréninku
        "max_iterations": 15,
        "max_triplets_per_iteration": 100,
        "min_improvement_threshold": 0.005,

        # Základní parametry tréninku
        "training_params": {
            "warmup_steps": 100,
            "evaluation_steps": 500,
            "checkpoint_steps": 500,
            "weight_decay": 0.01,
            "adam_epsilon": 1e-6
        }
    }

    with open(config_path, "w", encoding="utf-8") as f:
        json.dump(config, f, indent=2, ensure_ascii=False)

    return config


if __name__ == "__main__":
    import sys

    config_path = "config.json"

    if len(sys.argv) > 1:
        config_path = sys.argv[1]

    if not os.path.exists(config_path):
        print(f"Vytvářím výchozí konfiguraci: {config_path}")
        create_default_config(config_path)
        print("Upravte konfiguraci podle potřeby a spusťte znovu.")
        print(f"python {sys.argv[0]} {config_path}")
    else:
        print(f"Spouštím iterativní trénink s konfigurací: {config_path}")
        pipeline = IterativeTrainingPipeline(config_path)
        pipeline.run()