import torch
import numpy as np
from typing import Dict, List, Tuple, Optional
from sentence_transformers import SentenceTransformer, losses, evaluation
from torch.utils.data import DataLoader
import logging
import os
import json
from datetime import datetime
import pandas as pd


class EarlyStoppingCallback:
    """Early stopping callback pro sentence-transformers"""

    def __init__(self, patience: int = 3, min_delta: float = 0.001, mode: str = 'max'):
        self.patience = patience
        self.min_delta = min_delta
        self.mode = mode
        self.best_score = None
        self.counter = 0
        self.should_stop = False

    def __call__(self, score: float) -> bool:
        if self.best_score is None:
            self.best_score = score
        elif self._is_better(score):
            self.best_score = score
            self.counter = 0
        else:
            self.counter += 1
            if self.counter >= self.patience:
                self.should_stop = True
        return self.should_stop

    def _is_better(self, score: float) -> bool:
        if self.mode == 'max':
            return score > self.best_score + self.min_delta
        else:
            return score < self.best_score - self.min_delta


def create_loss_function(model: SentenceTransformer, train_params: Dict) -> losses.TripletLoss:
    """
    Vytvoří loss funkci podle parametrů iterace

    Args:
        model: Sentence transformer model
        train_params: Parametry obsahující typ loss a jeho nastavení

    Returns:
        Configured loss function
    """
    loss_type = train_params.get('loss_type', 'triplet')

    if loss_type == 'triplet':
        return losses.TripletLoss(
            model=model,
            distance_metric=train_params.get('distance_metric', 'cosine'),
            triplet_margin=train_params.get('margin', 0.5)
        )
    elif loss_type == 'online_triplet':
        return losses.OnlineContrastiveLoss(
            model=model,
            distance_metric=train_params.get('distance_metric', 'cosine'),
            margin=train_params.get('margin', 0.5)
        )
    elif loss_type == 'multiple_negatives':
        return losses.MultipleNegativesRankingLoss(
            model=model,
            scale=train_params.get('scale', 20.0),
            similarity_fct=train_params.get('similarity_fct', 'cos_sim')
        )
    else:
        raise ValueError(f"Neznámý typ loss funkce: {loss_type}")


def prepare_validation_data(val_data: Dict[str, List[str]], max_samples: int = 1000) -> Optional[
    evaluation.TripletEvaluator]:
    """
    Připraví validační evaluator z dat

    Args:
        val_data: Dictionary s anchors, positives, negatives
        max_samples: Maximální počet tripletů pro validaci

    Returns:
        TripletEvaluator nebo None
    """
    if not val_data or not all(k in val_data for k in ['anchors', 'positives', 'negatives']):
        return None

    # Omezíme počet validačních samplu pro rychlost
    n_samples = min(len(val_data['anchors']), max_samples)

    return evaluation.TripletEvaluator(
        anchors=val_data['anchors'][:n_samples],
        positives=val_data['positives'][:n_samples],
        negatives=val_data['negatives'][:n_samples],
        main_distance_function='cosine',
        name='validation'
    )


def save_training_metadata(output_path: str, train_params: Dict, triplets: List[Tuple[str, str, str]],
                           iteration_info: Dict = None):
    """
    Uloží metadata o tréninku pro debugging a analýzu

    Args:
        output_path: Výstupní složka
        train_params: Parametry tréninku
        triplets: Trénovací triplety
        iteration_info: Info o iteraci (průměrné vzdálenosti atd.)
    """
    metadata = {
        'timestamp': datetime.now().isoformat(),
        'train_params': train_params,
        'n_triplets': len(triplets),
        'iteration_info': iteration_info or {}
    }

    # Analýza tripletů
    if triplets:
        classes = {}
        for anchor, pos, neg in triplets:
            # Předpokládáme, že anchor obsahuje info o třídě
            anchor_key = anchor[:50]  # Zkrácený klíč pro groupování
            if anchor_key not in classes:
                classes[anchor_key] = 0
            classes[anchor_key] += 1

        metadata['triplet_distribution'] = classes

    # Uložení do JSON
    with open(os.path.join(output_path, 'training_metadata.json'), 'w', encoding='utf-8') as f:
        json.dump(metadata, f, indent=2, ensure_ascii=False)


def train_model(
        model: SentenceTransformer,
        triplets: List[Tuple[str, str, str]],
        output_path: str,
        train_params: Dict,
        val_data: Dict[str, List[str]] = None,
        iteration_info: Dict = None
) -> Tuple[SentenceTransformer, Dict]:
    """
    Hlavní trénovací funkce pro jednu iteraci s rozšířenou funkcionalitou

    Args:
        model: Výchozí model pro trénink
        triplets: Seznam tripletů (anchor, positive, negative)
        output_path: Cesta pro uložení modelu
        train_params: Parametry tréninku
        val_data: Validační data pro evaluaci
        iteration_info: Metadata o iteraci (průměrné vzdálenosti atd.)

    Returns:
        Tuple[trained_model, training_results]
    """

    # Validace vstupů
    if not triplets:
        raise ValueError("Seznam tripletů nesmí být prázdný")

    if not os.path.exists(output_path):
        os.makedirs(output_path, exist_ok=True)

    # Nastavení loggeru
    log_path = os.path.join(output_path, 'training_log.txt')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_path, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    logger = logging.getLogger(__name__)

    logger.info(f"Zahájení tréninku s {len(triplets)} triplety")
    logger.info(f"Parametry: {train_params}")
    if iteration_info:
        logger.info(f"Info o iteraci: {iteration_info}")

    # Uložení metadat
    save_training_metadata(output_path, train_params, triplets, iteration_info)

    try:
        # Příprava dataloaderu
        anchors = [t[0] for t in triplets]
        positives = [t[1] for t in triplets]
        negatives = [t[2] for t in triplets]

        train_dataloader = DataLoader(
            list(zip(anchors, positives, negatives)),
            shuffle=True,
            batch_size=train_params.get('batch_size', 16)
        )

        # Vytvoření loss funkce podle parametrů iterace
        train_loss = create_loss_function(model, train_params)
        logger.info(f"Použita loss funkce: {type(train_loss).__name__}")

        # Příprava evaluatoru
        evaluator = prepare_validation_data(val_data, max_samples=500)
        if evaluator:
            logger.info("Validační evaluator připraven")

        # Early stopping callback
        early_stopping = EarlyStoppingCallback(
            patience=train_params.get('patience', 3),
            min_delta=train_params.get('min_delta', 0.001),
            mode='max'
        )

        # Checkpoint callback
        checkpoint_path = os.path.join(output_path, 'checkpoints')
        os.makedirs(checkpoint_path, exist_ok=True)

        # Custom callback pro early stopping
        def evaluation_callback(score, epoch, steps):
            logger.info(f"Epoch {epoch}, Steps {steps}, Score: {score:.4f}")
            should_stop = early_stopping(score)
            if should_stop:
                logger.info(f"Early stopping triggered at epoch {epoch}")
            return should_stop

        # Trénink modelu
        logger.info("Spouštím trénink...")
        initial_lr = train_params.get('learning_rate', 2e-5)

        model.fit(
            train_objectives=[(train_dataloader, train_loss)],
            epochs=train_params.get('epochs', 10),
            warmup_steps=train_params.get('warmup_steps', int(len(train_dataloader) * 0.1)),
            optimizer_params={
                'lr': initial_lr,
                'eps': train_params.get('adam_epsilon', 1e-6),
                'weight_decay': train_params.get('weight_decay', 0.01)
            },
            evaluator=evaluator,
            evaluation_steps=train_params.get('evaluation_steps', 500),
            output_path=output_path,
            save_best_model=True,
            show_progress_bar=True,
            checkpoint_path=checkpoint_path,
            checkpoint_save_steps=train_params.get('checkpoint_steps', 500)
        )

        # Výsledky tréninku
        training_results = {
            'completed': True,
            'total_triplets': len(triplets),
            'final_lr': initial_lr,
            'early_stopped': early_stopping.should_stop,
            'best_score': early_stopping.best_score
        }

        logger.info("Trénink dokončen úspěšně")
        logger.info(f"Výsledky: {training_results}")

        # Uložení finálních výsledků
        with open(os.path.join(output_path, 'training_results.json'), 'w') as f:
            json.dump(training_results, f, indent=2)

        return model, training_results

    except Exception as e:
        logger.error(f"Chyba během tréninku: {e}")
        training_results = {
            'completed': False,
            'error': str(e),
            'total_triplets': len(triplets)
        }

        with open(os.path.join(output_path, 'training_results.json'), 'w') as f:
            json.dump(training_results, f, indent=2)

        raise e


def get_adaptive_params(avg_distance: float, iteration_num: int) -> Dict:
    """
    Generuje adaptivní parametry na základě průměrné vzdálenosti a čísla iterace

    Args:
        avg_distance: Průměrná vzdálenost tripletů v iteraci
        iteration_num: Číslo iterace (0-based)

    Returns:
        Dictionary s doporučenými parametry
    """
    # Mapování vzdálenosti na obtížnost (menší vzdálenost = těžší příklady)
    difficulty = 1.0 - min(avg_distance, 1.0)  # Normalizace do 0-1

    # Adaptivní parametry podle obtížnosti
    if difficulty > 0.7:  # Velmi těžké příklady
        params = {
            'learning_rate': 1e-5,  # Nižší LR pro stabilitu
            'batch_size': 8,  # Menší batche
            'margin': 0.3,  # Menší margin
            'epochs': 15,  # Více epoch
            'patience': 5,  # Více trpělivosti
            'loss_type': 'triplet'
        }
    elif difficulty > 0.4:  # Středně těžké příklady
        params = {
            'learning_rate': 2e-5,
            'batch_size': 16,
            'margin': 0.5,
            'epochs': 10,
            'patience': 3,
            'loss_type': 'triplet'
        }
    else:  # Lehčí příklady
        params = {
            'learning_rate': 3e-5,
            'batch_size': 32,
            'margin': 0.7,
            'epochs': 8,
            'patience': 2,
            'loss_type': 'online_triplet'  # Agresivnější mining
        }

    # Dodatečné úpravy podle čísla iterace
    if iteration_num > 5:  # Pozdější iterace - zjemňování
        params['learning_rate'] *= 0.5
        params['margin'] *= 0.8

    return params


# Ukázkové použití
if __name__ == "__main__":
    from SBertClassifier import SBertClassifier

    # Příklad použití
    classifier = SBertClassifier()

    # Dummy triplety pro test
    test_triplets = [
        ("Jaká je cena?", "Kolik to stojí?", "Kdy zavíráte?"),
        ("Otevírací doba", "Kdy máte otevřeno?", "Jaké máte ceny?")
    ]

    # Adaptivní parametry pro obtížnost 0.6
    params = get_adaptive_params(avg_distance=0.6, iteration_num=0)
    print(f"Doporučené parametry: {params}")

    # Spuštění tréninku (pouze pro ukázku)
    # trained_model, results = train_model(
    #     model=classifier.model,
    #     triplets=test_triplets,
    #     output_path="./test_output",
    #     train_params=params
    # )